"""
Data Collection Module for CS2 Betting Prediction System
"""
import os
import json
import pickle
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
from api.pandascore_client import PandaScoreClient
from api.hltv_client import HLTVClient
from config import config
from utils.logger import logger

class DataCollector:
    """Main data collection class that orchestrates data gathering from multiple sources"""
    
    def __init__(self):
        self.pandascore = PandaScoreClient()
        self.hltv = HLTVClient()
        self.cache_dir = config.data.cache_dir
        
    def collect_target_date_matches(self) -> List[Dict]:
        """Collect all matches for the target date (May 28th, 2025)"""
        logger.info(f"Collecting matches for {config.get_target_date_str()}")
        
        # Get matches from PandaScore
        pandascore_matches = self.pandascore.get_matches_by_date(
            config.prediction.target_date, 
            per_page=100
        )
        
        logger.info(f"Found {len(pandascore_matches)} matches from PandaScore")
        
        # Try to get matches from HLTV if available
        hltv_matches = []
        try:
            if self.hltv.health_check():
                hltv_data = self.hltv.get_matches_by_date(config.get_target_date_str())
                if hltv_data:
                    hltv_matches = hltv_data.get('upcomingMatchesByChampionship', [])
                    # Flatten the structure
                    flattened_matches = []
                    for championship in hltv_matches:
                        flattened_matches.extend(championship.get('matches', []))
                    hltv_matches = flattened_matches
                logger.info(f"Found {len(hltv_matches)} matches from HLTV")
            else:
                logger.warning("HLTV API not available, using PandaScore only")
        except Exception as e:
            logger.error("Error fetching HLTV matches", e)
        
        # Combine and deduplicate matches
        all_matches = self._combine_match_data(pandascore_matches, hltv_matches)
        
        # Cache the results
        self._cache_data('target_matches', all_matches)
        
        logger.info(f"Total unique matches found: {len(all_matches)}")
        return all_matches
    
    def collect_team_data(self, team_names: List[str]) -> Dict[str, Dict]:
        """Collect comprehensive team data for analysis"""
        logger.info(f"Collecting data for {len(team_names)} teams")
        
        team_data = {}
        
        for team_name in team_names:
            logger.info(f"Processing team: {team_name}")
            
            # Get team data from PandaScore
            ps_teams = self.pandascore.search_teams(team_name)
            ps_team = ps_teams[0] if ps_teams else None
            
            # Get team data from HLTV if available
            hltv_team = None
            try:
                if self.hltv.health_check():
                    hltv_teams = self.hltv.search_teams(team_name)
                    hltv_team = hltv_teams[0] if hltv_teams else None
            except Exception as e:
                logger.error(f"Error fetching HLTV team data for {team_name}", e)
            
            # Combine team data
            combined_data = self._combine_team_data(ps_team, hltv_team, team_name)
            team_data[team_name] = combined_data
            
            # Get recent matches for the team
            if ps_team:
                recent_matches = self.pandascore.get_team_matches(ps_team['id'], per_page=20)
                combined_data['recent_matches'] = recent_matches
            
        # Cache team data
        self._cache_data('team_data', team_data)
        
        return team_data
    
    def collect_historical_data(self, days_back: int = 90) -> Dict:
        """Collect historical match data for analysis"""
        logger.info(f"Collecting historical data for last {days_back} days")
        
        historical_data = {
            'matches': [],
            'teams': {},
            'players': {}
        }
        
        # Get recent matches from PandaScore
        try:
            # PandaScore doesn't have a direct "recent matches" endpoint
            # So we'll get upcoming matches and some team matches
            upcoming = self.pandascore.get_upcoming_matches(per_page=100)
            historical_data['matches'].extend(upcoming)
            
            # Get teams and their recent matches
            teams = self.pandascore.get_teams(per_page=50)
            for team in teams[:20]:  # Limit to top 20 teams for now
                team_matches = self.pandascore.get_team_matches(team['id'], per_page=10)
                historical_data['teams'][team['name']] = {
                    'info': team,
                    'recent_matches': team_matches
                }
                
        except Exception as e:
            logger.error("Error collecting historical data", e)
        
        # Cache historical data
        self._cache_data('historical_data', historical_data)
        
        return historical_data
    
    def _combine_match_data(self, ps_matches: List[Dict], hltv_matches: List[Dict]) -> List[Dict]:
        """Combine and deduplicate match data from different sources"""
        combined = []
        seen_matches = set()
        
        # Process PandaScore matches
        for match in ps_matches:
            match_key = self._generate_match_key(match, 'pandascore')
            if match_key not in seen_matches:
                match['source'] = 'pandascore'
                combined.append(match)
                seen_matches.add(match_key)
        
        # Process HLTV matches
        for match in hltv_matches:
            match_key = self._generate_match_key(match, 'hltv')
            if match_key not in seen_matches:
                match['source'] = 'hltv'
                combined.append(match)
                seen_matches.add(match_key)
        
        return combined
    
    def _generate_match_key(self, match: Dict, source: str) -> str:
        """Generate a unique key for match deduplication"""
        if source == 'pandascore':
            opponents = match.get('opponents', [])
            if len(opponents) >= 2:
                team1 = opponents[0].get('opponent', {}).get('name', '')
                team2 = opponents[1].get('opponent', {}).get('name', '')
                date_str = match.get('begin_at', '')[:10]  # YYYY-MM-DD
                return f"{team1}_vs_{team2}_{date_str}".lower()
        elif source == 'hltv':
            team1 = match.get('team1', {}).get('name', '')
            team2 = match.get('team2', {}).get('name', '')
            date_str = str(match.get('date', ''))[:10]
            return f"{team1}_vs_{team2}_{date_str}".lower()
        
        return f"unknown_{source}_{match.get('id', 'no_id')}"
    
    def _combine_team_data(self, ps_team: Optional[Dict], hltv_team: Optional[Dict], team_name: str) -> Dict:
        """Combine team data from different sources"""
        combined = {
            'name': team_name,
            'pandascore_data': ps_team,
            'hltv_data': hltv_team,
            'combined_stats': {}
        }
        
        # Extract key information
        if ps_team:
            combined['id'] = ps_team.get('id')
            combined['acronym'] = ps_team.get('acronym')
            combined['location'] = ps_team.get('location')
            combined['players'] = ps_team.get('players', [])
        
        if hltv_team:
            combined['hltv_id'] = hltv_team.get('id')
            combined['ranking'] = hltv_team.get('ranking')
            combined['country'] = hltv_team.get('country')
        
        return combined
    
    def _cache_data(self, key: str, data: Any) -> None:
        """Cache data to disk"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{key}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
            logger.info(f"Cached {key} data to {cache_file}")
        except Exception as e:
            logger.error(f"Error caching {key} data", e)
    
    def load_cached_data(self, key: str, max_age_hours: int = 24) -> Optional[Any]:
        """Load cached data if it exists and is recent enough"""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.startswith(f"{key}_")]
            if not cache_files:
                return None
            
            # Get the most recent cache file
            cache_files.sort(reverse=True)
            latest_file = cache_files[0]
            
            # Check if it's recent enough
            file_path = os.path.join(self.cache_dir, latest_file)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            if datetime.now() - file_time > timedelta(hours=max_age_hours):
                return None
            
            # Load the data
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"Loaded cached {key} data from {latest_file}")
            return data
            
        except Exception as e:
            logger.error(f"Error loading cached {key} data", e)
            return None
