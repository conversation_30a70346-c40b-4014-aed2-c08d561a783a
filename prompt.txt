# CS2 Betting Prediction System - Development Instructions

## Project Overview
Build a comprehensive CS2 betting prediction system that combines multiple data sources and advanced analytics to generate high-confidence betting recommendations for May 28th, 2025.

## Phase 1: Repository Setup & Analysis

### 1.1 HLTV API Integration
- Clone and set up the HLTV API server locally: https://github.com/Giantcard5/hltv-api
- Thoroughly analyze the project structure, available endpoints, and data schemas
- Document all available data points including:
  - Team statistics (recent form, head-to-head records, map performance)
  - Player statistics (individual performance metrics, recent form)
  - Match history and results
  - Map pool data and win rates
  - Tournament context and importance

### 1.2 PandaScore API Integration
- Set up PandaScore API integration for upcoming matches
- API DOC: https://developers.pandascore.co/reference/get_csgo_matches_upcoming-1
- API ENDPOINT: get https://api.pandascore.co/csgo/matches/upcoming
- API Key: `3OUT3I38zgI8Xl411w1L8_B4R7UmxLpPnWQF2ShtIw9V7MvFvBQ`
- Fetch all CS2 matches scheduled for May 28th, 2025
- Extract comprehensive match metadata (teams, tournament, format, etc.)

## Phase 2: Advanced Betting Logic Implementation

### 2.1 Core Betting Types to Implement
1. **Match Winner** - Moneyline predictions
2. **Map Handicap** - Spread betting on individual maps
3. **Total Maps** - Over/under on total maps played
4. **First Map Winner** - Predictions for opening map
5. **Exact Score** - Precise match score predictions
6. **Team to Win at Least One Map** - Safety bets

### 2.2 Statistical Models & Algorithms

#### 2.2.1 Team Performance Metrics
- **Recent Form Analysis** (last 10-15 matches)
  - Win/loss streaks and momentum
  - Performance against similar-tier opponents
  - Map-specific win rates and tendencies
- **Head-to-Head Historical Data**
  - Direct matchup history (last 6 months)
  - Map preferences in head-to-head play
  - Score patterns and tendencies
- **Map Pool Analysis**
  - Individual map win rates for each team
  - Map ban/pick patterns and strategies
  - Recent map-specific performance trends

#### 2.2.2 Advanced Analytics
- **ELO Rating System Implementation**
  - Dynamic team ratings based on recent performance
  - Opponent strength adjustments
  - Map-specific ELO calculations
- **Monte Carlo Simulations**
  - Run 10,000+ simulations per match
  - Factor in variance and upset probability
  - Generate confidence intervals for predictions
- **Player Impact Analysis**
  - Key player form and availability
  - Star player performance correlation with team success
  - Roster changes and their historical impact

#### 2.2.3 Contextual Factors
- **Tournament Importance Weighting**
  - Major vs. minor tournament motivation levels
  - Prize pool and ranking point implications
  - Team preparation time and focus
- **Scheduling & Travel Factors**
  - Jet lag and travel fatigue analysis
  - Back-to-back match scheduling impacts
  - Home/away advantage considerations
- **Meta & Patch Analysis**
  - Current game version adaptation rates
  - Recent balance changes impact on team styles
  - Map pool changes and team adaptability

### 2.3 Betting Logic Framework

#### 2.3.1 Confidence Scoring System
- Implement a 1-100 confidence scale for each prediction
- Minimum 70% confidence threshold for recommendations
- Weight predictions based on data quality and recency
- Account for sample size and statistical significance

#### 2.3.2 Value Betting Strategy
- Compare model predictions with implied market odds
- Identify positive expected value opportunities
- Implement Kelly Criterion for bet sizing recommendations
- Risk management through diversification

#### 2.3.3 Multi-Model Ensemble
- Combine multiple prediction models:
  - Statistical regression models
  - Machine learning algorithms (Random Forest, XGBoost)
  - Deep learning neural networks for pattern recognition
- Use weighted averaging based on historical model performance
- Implement model validation and backtesting

## Phase 3: Data Processing & Feature Engineering

### 3.1 Data Collection Pipeline
- Automate data fetching from both APIs
- Implement error handling and retry mechanisms
- Set up data validation and quality checks
- Create data caching to minimize API calls

### 3.2 Feature Engineering
- **Time-weighted statistics** (recent matches weighted more heavily)
- **Opponent-adjusted metrics** (performance vs. strong/weak opponents)
- **Situational features** (elimination games, tournament stages)
- **Streaks and momentum indicators**
- **Map-specific team strengths and weaknesses**

### 3.3 Data Preprocessing
- Handle missing data intelligently
- Normalize statistics across different time periods
- Create composite metrics (e.g., recent form + historical matchup)
- Feature selection to avoid overfitting

## Phase 4: Output & Recommendations

### 4.1 Top 5 Betting Recommendations Format
For each recommended bet, provide:
```
Bet #X: [Bet Type]
Match: Team A vs Team B
Recommendation: [Specific bet details]
Confidence: X%
Expected Value: +X%
Reasoning: [2-3 sentence explanation of key factors]
Suggested Stake: X% of bankroll (Kelly Criterion)
```

### 4.2 Additional Analysis
- Provide alternative betting options for each match
- Risk assessment for each recommendation
- Correlation analysis between recommended bets
- Market timing recommendations (when to place bets)

## Phase 5: System Architecture & Best Practices

### 5.1 Code Structure
- Modular design with separate components for:
  - Data fetching and API management
  - Statistical analysis and modeling
  - Prediction generation
  - Output formatting and reporting
- Comprehensive error handling and logging
- Unit tests for critical functions

### 5.2 Performance Optimization
- Efficient data processing using vectorized operations
- Parallel processing for multiple match analysis
- Caching strategies to reduce computation time
- Memory-efficient data structures

### 5.3 Documentation & Maintenance
- Clear code documentation and comments
- Configuration files for easy parameter adjustments
- Logging system for debugging and performance monitoring
- Version control with meaningful commit messages

## Success Criteria
- Successfully analyze all May 28th, 2025 CS2 matches
- Generate exactly 5 high-confidence betting recommendations
- Achieve minimum 70% confidence on all recommendations
- Provide clear, actionable betting advice with proper risk management
- Implement at least 3 different bet types from the specified list
- Create a reusable system for future betting analysis

## Technical Requirements
- Use Python with pandas, numpy, scikit-learn, and requests libraries
- Implement proper API rate limiting and error handling
- Create clean, maintainable, and well-documented code
- Include comprehensive testing and validation procedures

**Note**: Focus on creating a robust, data-driven system that prioritizes accuracy and risk management over quantity of bets. Quality predictions with proper confidence intervals are more valuable than numerous low-confidence recommendations.