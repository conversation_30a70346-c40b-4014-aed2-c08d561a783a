"""
PandaScore API Client for CS2 Betting Prediction System
"""
import requests
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, date
from config import config
from utils.logger import logger

class PandaScoreClient:
    """Client for interacting with the PandaScore API"""
    
    def __init__(self):
        self.base_url = config.api.pandascore_base_url
        self.api_key = config.api.pandascore_api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Accept': 'application/json',
            'User-Agent': 'CS2-Betting-Predictor/1.0'
        })
        self.last_request_time = 0
        self.rate_limit_delay = 3600 / config.api.pandascore_rate_limit  # seconds between requests
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make a request to the PandaScore API with error handling and retries"""
        self._rate_limit()
        
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(config.api.max_retries):
            try:
                start_time = time.time()
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=config.api.timeout
                )
                response_time = time.time() - start_time
                
                logger.log_api_call(endpoint, response.status_code, response_time)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limited
                    logger.warning(f"Rate limited on {endpoint}, waiting...")
                    time.sleep(config.api.retry_delay * (attempt + 1) * 2)
                    continue
                elif response.status_code == 401:
                    logger.error(f"Authentication failed for {endpoint}")
                    return None
                else:
                    logger.error(f"HTTP {response.status_code} for {endpoint}: {response.text}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request failed for {endpoint}", e)
                if attempt < config.api.max_retries - 1:
                    time.sleep(config.api.retry_delay * (attempt + 1))
                    continue
                return None
        
        return None
    
    def get_upcoming_matches(self, per_page: int = 50, page: int = 1) -> List[Dict]:
        """Get upcoming CS:GO matches"""
        params = {
            'per_page': per_page,
            'page': page,
            'sort': 'begin_at'
        }
        data = self._make_request("/csgo/matches/upcoming", params)
        return data if isinstance(data, list) else []
    
    def get_matches_by_date(self, target_date: date, per_page: int = 50) -> List[Dict]:
        """Get matches for a specific date"""
        date_str = target_date.strftime("%Y-%m-%d")
        params = {
            'filter[begin_at]': f"{date_str}T00:00:00Z,{date_str}T23:59:59Z",
            'per_page': per_page,
            'sort': 'begin_at'
        }
        data = self._make_request("/csgo/matches/upcoming", params)
        return data if isinstance(data, list) else []
    
    def get_match_details(self, match_id: int) -> Optional[Dict]:
        """Get detailed information about a specific match"""
        return self._make_request(f"/csgo/matches/{match_id}")
    
    def get_teams(self, per_page: int = 50, page: int = 1) -> List[Dict]:
        """Get CS:GO teams"""
        params = {
            'per_page': per_page,
            'page': page,
            'sort': 'name'
        }
        data = self._make_request("/csgo/teams", params)
        return data if isinstance(data, list) else []
    
    def get_team_details(self, team_id: int) -> Optional[Dict]:
        """Get detailed information about a specific team"""
        return self._make_request(f"/csgo/teams/{team_id}")
    
    def get_team_matches(self, team_id: int, per_page: int = 20) -> List[Dict]:
        """Get recent matches for a team"""
        params = {
            'per_page': per_page,
            'sort': '-begin_at'
        }
        data = self._make_request(f"/csgo/teams/{team_id}/matches", params)
        return data if isinstance(data, list) else []
    
    def get_players(self, per_page: int = 50, page: int = 1) -> List[Dict]:
        """Get CS:GO players"""
        params = {
            'per_page': per_page,
            'page': page,
            'sort': 'name'
        }
        data = self._make_request("/csgo/players", params)
        return data if isinstance(data, list) else []
    
    def get_player_details(self, player_id: int) -> Optional[Dict]:
        """Get detailed information about a specific player"""
        return self._make_request(f"/csgo/players/{player_id}")
    
    def get_tournaments(self, per_page: int = 50, page: int = 1) -> List[Dict]:
        """Get CS:GO tournaments"""
        params = {
            'per_page': per_page,
            'page': page,
            'sort': '-begin_at'
        }
        data = self._make_request("/csgo/tournaments", params)
        return data if isinstance(data, list) else []
    
    def get_tournament_details(self, tournament_id: int) -> Optional[Dict]:
        """Get detailed information about a specific tournament"""
        return self._make_request(f"/csgo/tournaments/{tournament_id}")
    
    def get_tournament_matches(self, tournament_id: int, per_page: int = 50) -> List[Dict]:
        """Get matches for a specific tournament"""
        params = {
            'per_page': per_page,
            'sort': 'begin_at'
        }
        data = self._make_request(f"/csgo/tournaments/{tournament_id}/matches", params)
        return data if isinstance(data, list) else []
    
    def get_series(self, per_page: int = 50, page: int = 1) -> List[Dict]:
        """Get CS:GO series"""
        params = {
            'per_page': per_page,
            'page': page,
            'sort': '-begin_at'
        }
        data = self._make_request("/csgo/series", params)
        return data if isinstance(data, list) else []
    
    def get_series_details(self, series_id: int) -> Optional[Dict]:
        """Get detailed information about a specific series"""
        return self._make_request(f"/csgo/series/{series_id}")
    
    def get_games(self, match_id: int) -> List[Dict]:
        """Get games (maps) for a specific match"""
        data = self._make_request(f"/csgo/matches/{match_id}/games")
        return data if isinstance(data, list) else []
    
    def search_teams(self, name: str) -> List[Dict]:
        """Search for teams by name"""
        params = {
            'search[name]': name,
            'per_page': 20
        }
        data = self._make_request("/csgo/teams", params)
        return data if isinstance(data, list) else []
    
    def search_players(self, name: str) -> List[Dict]:
        """Search for players by name"""
        params = {
            'search[name]': name,
            'per_page': 20
        }
        data = self._make_request("/csgo/players", params)
        return data if isinstance(data, list) else []
    
    def get_leagues(self, per_page: int = 50, page: int = 1) -> List[Dict]:
        """Get CS:GO leagues"""
        params = {
            'per_page': per_page,
            'page': page,
            'sort': 'name'
        }
        data = self._make_request("/csgo/leagues", params)
        return data if isinstance(data, list) else []
    
    def health_check(self) -> bool:
        """Check if the PandaScore API is responding"""
        try:
            response = self.session.get(f"{self.base_url}/csgo/teams", 
                                      params={'per_page': 1}, 
                                      timeout=5)
            return response.status_code == 200
        except:
            return False
