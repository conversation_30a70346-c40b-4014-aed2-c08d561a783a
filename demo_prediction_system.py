"""
Demo CS2 Betting Prediction System with Simulated Data
"""
import sys
import os
import json
import random
from typing import Dict, List
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analysis.statistical_engine import StatisticalEngine
from analysis.monte_carlo import MonteCarloSimulator
from betting.strategy_engine import BettingStrategyEngine, BettingRecommendation
from config import config
from utils.logger import logger

class DemoCS2BettingPredictor:
    """Demo prediction system with simulated team data"""
    
    def __init__(self):
        self.statistical_engine = StatisticalEngine()
        self.monte_carlo = MonteCarloSimulator()
        self.betting_engine = BettingStrategyEngine()
        
        logger.info("Demo CS2 Betting Prediction System initialized")
    
    def run_demo_analysis(self) -> Dict:
        """Run demo analysis with simulated data"""
        logger.info("Starting demo analysis for CS2 betting predictions")
        
        try:
            # Load real matches from cache
            matches = self._load_real_matches()
            
            if not matches:
                logger.error("No cached matches found")
                return {'error': 'No cached matches found'}
            
            # Generate simulated team data for the teams in the matches
            team_names = self._extract_team_names(matches)
            simulated_team_data = self._generate_simulated_team_data(team_names)
            
            # Analyze matches with simulated data
            match_predictions = self._analyze_matches_with_simulated_data(matches, simulated_team_data)
            
            # Generate betting recommendations
            recommendations = self.betting_engine.generate_recommendations(match_predictions)
            
            # Compile results
            results = {
                'analysis_date': datetime.now().isoformat(),
                'target_date': config.get_target_date_str(),
                'total_matches_analyzed': len(matches),
                'teams_analyzed': len(team_names),
                'recommendations': self._format_recommendations(recommendations),
                'match_predictions': match_predictions,
                'portfolio_analysis': self.betting_engine.calculate_portfolio_risk(recommendations),
                'system_confidence': self._calculate_system_confidence(match_predictions),
                'demo_mode': True
            }
            
            logger.info("Demo analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error("Error during demo analysis", e)
            return {'error': str(e)}
    
    def _load_real_matches(self) -> List[Dict]:
        """Load real matches from cache"""
        try:
            cache_files = [f for f in os.listdir(config.data.cache_dir) if f.startswith('target_matches_')]
            if not cache_files:
                return []
            
            latest_file = sorted(cache_files)[-1]
            file_path = os.path.join(config.data.cache_dir, latest_file)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                matches = json.load(f)
            
            logger.info(f"Loaded {len(matches)} matches from {latest_file}")
            return matches
            
        except Exception as e:
            logger.error("Error loading cached matches", e)
            return []
    
    def _extract_team_names(self, matches: List[Dict]) -> List[str]:
        """Extract unique team names from matches"""
        team_names = set()
        
        for match in matches:
            if match.get('source') == 'hltv':
                team1 = match.get('team1', {}).get('name')
                team2 = match.get('team2', {}).get('name')
                if team1:
                    team_names.add(team1)
                if team2:
                    team_names.add(team2)
        
        return list(team_names)
    
    def _generate_simulated_team_data(self, team_names: List[str]) -> Dict:
        """Generate simulated team data for demo purposes"""
        simulated_data = {}
        
        # Define some tier levels for more realistic simulation
        tier_1_teams = ['Sangal', 'Sashi', 'CPH Wolves']  # Higher skill teams
        tier_2_teams = ['CYBERSHOKE', 'ENCE Academy', 'UNiTY', 'WOPA']  # Mid-tier teams
        
        for team_name in team_names:
            # Determine team tier
            if team_name in tier_1_teams:
                base_skill = 0.65  # Higher win rate
                consistency = 0.8
            elif team_name in tier_2_teams:
                base_skill = 0.55  # Medium win rate
                consistency = 0.6
            else:
                base_skill = 0.45  # Lower tier teams
                consistency = 0.4
            
            # Add some randomness
            skill_variance = random.uniform(-0.1, 0.1)
            final_skill = max(0.2, min(0.8, base_skill + skill_variance))
            
            # Generate simulated recent matches
            recent_matches = self._generate_simulated_matches(team_name, final_skill, 12)
            
            simulated_data[team_name] = {
                'name': team_name,
                'recent_matches': recent_matches,
                'simulated_skill_level': final_skill,
                'simulated_consistency': consistency,
                'players': self._generate_simulated_players(team_name)
            }
        
        logger.info(f"Generated simulated data for {len(team_names)} teams")
        return simulated_data
    
    def _generate_simulated_matches(self, team_name: str, skill_level: float, num_matches: int) -> List[Dict]:
        """Generate simulated match history for a team"""
        matches = []
        
        for i in range(num_matches):
            # Simulate match outcome based on skill level
            won = random.random() < skill_level
            
            match = {
                'id': f'sim_{team_name}_{i}',
                'winner': {'name': team_name if won else 'Opponent'},
                'opponents': [
                    {'opponent': {'name': team_name}},
                    {'opponent': {'name': f'Opponent_{i}'}}
                ],
                'games': [
                    {
                        'map': {'name': random.choice(['de_dust2', 'de_mirage', 'de_inferno', 'de_cache'])},
                        'winner': {'name': team_name if won else f'Opponent_{i}'}
                    }
                ]
            }
            matches.append(match)
        
        return matches
    
    def _generate_simulated_players(self, team_name: str) -> List[Dict]:
        """Generate simulated player data"""
        player_names = [f'{team_name}_Player_{i}' for i in range(1, 6)]
        players = []
        
        for name in player_names:
            players.append({
                'name': name,
                'active': True,
                'age': random.randint(18, 28),
                'nationality': random.choice(['US', 'EU', 'BR', 'RU']),
                'role': random.choice(['AWPer', 'Entry', 'Support', 'IGL', 'Rifler'])
            })
        
        return players
    
    def _analyze_matches_with_simulated_data(self, matches: List[Dict], team_data: Dict) -> List[Dict]:
        """Analyze matches using simulated team data"""
        predictions = []
        
        for match in matches:
            try:
                team1_name = match.get('team1', {}).get('name')
                team2_name = match.get('team2', {}).get('name')
                
                if not team1_name or not team2_name:
                    continue
                
                team1_data = team_data.get(team1_name, {'name': team1_name, 'recent_matches': []})
                team2_data = team_data.get(team2_name, {'name': team2_name, 'recent_matches': []})
                
                # Generate statistical prediction
                stat_prediction = self.statistical_engine.predict_match_outcome(team1_data, team2_data)
                
                # Run Monte Carlo simulation
                match_format = match.get('format', 'bo3')
                mc_results = self.monte_carlo.simulate_match(
                    stat_prediction['team1_win_probability'],
                    stat_prediction['team2_win_probability'],
                    match_format
                )
                
                # Enhance confidence based on simulated data quality
                enhanced_confidence = min(0.85, stat_prediction['confidence'] + 0.2)  # Boost for demo
                
                combined_prediction = {
                    'match_info': {
                        'team1': team1_name,
                        'team2': team2_name,
                        'format': match_format,
                        'date': match.get('date'),
                        'tournament': match.get('event', {}).get('name'),
                        'source': match.get('source')
                    },
                    'team1_win_probability': mc_results['team1_win_probability'],
                    'team2_win_probability': mc_results['team2_win_probability'],
                    'confidence': enhanced_confidence,
                    'statistical_analysis': stat_prediction,
                    'monte_carlo_results': mc_results,
                    'upset_probability': mc_results['upset_probability']
                }
                
                predictions.append(combined_prediction)
                
                logger.info(f"Analyzed match: {team1_name} vs {team2_name} - Confidence: {enhanced_confidence:.2%}")
                
            except Exception as e:
                logger.error(f"Error analyzing match: {match}", e)
                continue
        
        return predictions
    
    def _format_recommendations(self, recommendations: List[BettingRecommendation]) -> List[Dict]:
        """Format recommendations for output"""
        formatted = []
        
        for i, rec in enumerate(recommendations, 1):
            formatted.append({
                'bet_number': i,
                'bet_type': rec.bet_type,
                'match': rec.match,
                'recommendation': rec.recommendation,
                'confidence': f"{rec.confidence:.1%}",
                'expected_value': f"{rec.expected_value:.1%}",
                'reasoning': rec.reasoning,
                'suggested_stake': f"{rec.suggested_stake:.1%}",
                'risk_level': rec.risk_level,
                'odds': rec.odds
            })
        
        return formatted
    
    def _calculate_system_confidence(self, predictions: List[Dict]) -> float:
        """Calculate overall system confidence"""
        if not predictions:
            return 0.0
        
        confidences = [pred['confidence'] for pred in predictions]
        return sum(confidences) / len(confidences)
    
    def print_demo_recommendations(self, results: Dict) -> None:
        """Print formatted demo recommendations to console"""
        if 'error' in results:
            print(f"❌ Error: {results['error']}")
            return
        
        recommendations = results.get('recommendations', [])
        
        print("\n" + "="*80)
        print("🎯 CS2 BETTING PREDICTIONS - DEMO MODE")
        print("⚠️  Using simulated team data for demonstration purposes")
        print(f"📅 Target Date: {results['target_date']}")
        print(f"🔍 Matches Analyzed: {results['total_matches_analyzed']}")
        print(f"📊 System Confidence: {results['system_confidence']:.1%}")
        print("="*80)
        
        if not recommendations:
            print("\n❌ No high-confidence recommendations generated in demo mode.")
            return
        
        for rec in recommendations:
            print(f"\n🎲 Bet #{rec['bet_number']}: {rec['bet_type']}")
            print(f"⚽ Match: {rec['match']}")
            print(f"💡 Recommendation: {rec['recommendation']}")
            print(f"🎯 Confidence: {rec['confidence']}")
            print(f"💰 Expected Value: {rec['expected_value']}")
            print(f"📈 Suggested Stake: {rec['suggested_stake']} of bankroll")
            print(f"⚠️  Risk Level: {rec['risk_level']}")
            print(f"🧠 Reasoning: {rec['reasoning']}")
            if rec.get('odds'):
                print(f"📊 Estimated Odds: {rec['odds']:.2f}")
            print("-" * 60)
        
        # Portfolio analysis
        portfolio = results.get('portfolio_analysis', {})
        print(f"\n📊 PORTFOLIO ANALYSIS")
        print(f"💼 Total Stake: {portfolio.get('total_stake', 0):.1%} of bankroll")
        print(f"🎯 Diversification Score: {portfolio.get('diversification_score', 0):.1%}")
        print(f"⚠️  Portfolio Risk: {portfolio.get('total_risk', 0):.1%}")
        
        print("\n" + "="*80)
        print("⚠️  DEMO DISCLAIMER: This demo uses simulated team data.")
        print("   Real predictions would require comprehensive historical data.")
        print("   Always gamble responsibly and never bet more than you can afford to lose.")
        print("="*80)

def main():
    """Main function to run the demo prediction system"""
    print("🚀 Starting CS2 Betting Prediction System - DEMO MODE...")
    
    predictor = DemoCS2BettingPredictor()
    results = predictor.run_demo_analysis()
    predictor.print_demo_recommendations(results)

if __name__ == "__main__":
    main()
