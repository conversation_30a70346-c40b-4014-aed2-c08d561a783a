"""
Betting Strategy Engine for CS2 Predictions
"""
import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from config import config
from utils.logger import logger

@dataclass
class BettingRecommendation:
    """Data class for betting recommendations"""
    bet_type: str
    match: str
    recommendation: str
    confidence: float
    expected_value: float
    reasoning: str
    suggested_stake: float
    odds: Optional[float] = None
    risk_level: str = "Medium"

class BettingStrategyEngine:
    """Main betting strategy and recommendation engine"""
    
    def __init__(self):
        self.min_confidence = config.prediction.min_confidence_threshold
        self.min_ev = config.betting.min_expected_value
        self.kelly_fraction = config.betting.kelly_fraction
        self.max_bet_size = config.betting.max_bet_size
    
    def generate_recommendations(self, match_predictions: List[Dict]) -> List[BettingRecommendation]:
        """Generate top betting recommendations from match predictions"""
        logger.info(f"Generating recommendations from {len(match_predictions)} match predictions")
        
        all_recommendations = []
        
        for prediction in match_predictions:
            match_recommendations = self._analyze_match_betting_opportunities(prediction)
            all_recommendations.extend(match_recommendations)
        
        # Filter by confidence and expected value
        filtered_recommendations = [
            rec for rec in all_recommendations 
            if rec.confidence >= self.min_confidence and rec.expected_value >= self.min_ev
        ]
        
        # Sort by expected value and confidence
        filtered_recommendations.sort(
            key=lambda x: (x.expected_value * x.confidence), 
            reverse=True
        )
        
        # Return top 5 recommendations
        top_recommendations = filtered_recommendations[:5]
        
        logger.info(f"Generated {len(top_recommendations)} high-confidence recommendations")
        return top_recommendations
    
    def _analyze_match_betting_opportunities(self, prediction: Dict) -> List[BettingRecommendation]:
        """Analyze betting opportunities for a single match"""
        recommendations = []
        
        match_info = prediction['match_info']
        team1 = match_info['team1']
        team2 = match_info['team2']
        match_name = f"{team1} vs {team2}"
        
        # Get prediction probabilities
        team1_prob = prediction['team1_win_probability']
        team2_prob = prediction['team2_win_probability']
        confidence = prediction['confidence']
        
        # Simulate market odds (in a real system, these would come from bookmakers)
        estimated_odds = self._estimate_market_odds(team1_prob, team2_prob)
        
        # Analyze different bet types
        recommendations.extend(self._analyze_match_winner_bets(
            match_name, team1, team2, team1_prob, team2_prob, confidence, estimated_odds
        ))
        
        recommendations.extend(self._analyze_map_handicap_bets(
            match_name, team1, team2, prediction, confidence
        ))
        
        recommendations.extend(self._analyze_total_maps_bets(
            match_name, prediction, confidence
        ))
        
        recommendations.extend(self._analyze_first_map_bets(
            match_name, team1, team2, prediction, confidence
        ))
        
        return recommendations
    
    def _analyze_match_winner_bets(self, match_name: str, team1: str, team2: str,
                                  team1_prob: float, team2_prob: float, 
                                  confidence: float, estimated_odds: Dict) -> List[BettingRecommendation]:
        """Analyze match winner betting opportunities"""
        recommendations = []
        
        # Team 1 to win
        team1_odds = estimated_odds['team1_win']
        team1_ev = self._calculate_expected_value(team1_prob, team1_odds)
        
        if team1_ev > self.min_ev and confidence >= self.min_confidence:
            stake = self._calculate_kelly_stake(team1_prob, team1_odds)
            recommendations.append(BettingRecommendation(
                bet_type="Match Winner",
                match=match_name,
                recommendation=f"{team1} to win",
                confidence=confidence,
                expected_value=team1_ev,
                reasoning=f"{team1} has {team1_prob:.1%} win probability with strong recent form and favorable matchup.",
                suggested_stake=stake,
                odds=team1_odds,
                risk_level=self._assess_risk_level(confidence, team1_ev)
            ))
        
        # Team 2 to win
        team2_odds = estimated_odds['team2_win']
        team2_ev = self._calculate_expected_value(team2_prob, team2_odds)
        
        if team2_ev > self.min_ev and confidence >= self.min_confidence:
            stake = self._calculate_kelly_stake(team2_prob, team2_odds)
            recommendations.append(BettingRecommendation(
                bet_type="Match Winner",
                match=match_name,
                recommendation=f"{team2} to win",
                confidence=confidence,
                expected_value=team2_ev,
                reasoning=f"{team2} has {team2_prob:.1%} win probability with excellent map pool coverage.",
                suggested_stake=stake,
                odds=team2_odds,
                risk_level=self._assess_risk_level(confidence, team2_ev)
            ))
        
        return recommendations
    
    def _analyze_map_handicap_bets(self, match_name: str, team1: str, team2: str,
                                  prediction: Dict, confidence: float) -> List[BettingRecommendation]:
        """Analyze map handicap betting opportunities"""
        recommendations = []
        
        # Simulate map handicap analysis
        team1_prob = prediction['team1_win_probability']
        
        # If team heavily favored, look at handicap bets
        if team1_prob > 0.65:
            handicap_prob = team1_prob * 0.8  # Reduce probability for handicap
            handicap_odds = 1 / handicap_prob * 1.1  # Add bookmaker margin
            ev = self._calculate_expected_value(handicap_prob, handicap_odds)
            
            if ev > self.min_ev:
                stake = self._calculate_kelly_stake(handicap_prob, handicap_odds)
                recommendations.append(BettingRecommendation(
                    bet_type="Map Handicap",
                    match=match_name,
                    recommendation=f"{team1} -1.5 maps",
                    confidence=confidence * 0.9,  # Slightly lower confidence for handicap
                    expected_value=ev,
                    reasoning=f"{team1} is heavily favored and likely to win convincingly based on recent form.",
                    suggested_stake=stake,
                    odds=handicap_odds,
                    risk_level=self._assess_risk_level(confidence * 0.9, ev)
                ))
        
        elif team1_prob < 0.35:
            handicap_prob = (1 - team1_prob) * 0.8
            handicap_odds = 1 / handicap_prob * 1.1
            ev = self._calculate_expected_value(handicap_prob, handicap_odds)
            
            if ev > self.min_ev:
                stake = self._calculate_kelly_stake(handicap_prob, handicap_odds)
                recommendations.append(BettingRecommendation(
                    bet_type="Map Handicap",
                    match=match_name,
                    recommendation=f"{team2} -1.5 maps",
                    confidence=confidence * 0.9,
                    expected_value=ev,
                    reasoning=f"{team2} is heavily favored and likely to win convincingly based on superior map pool.",
                    suggested_stake=stake,
                    odds=handicap_odds,
                    risk_level=self._assess_risk_level(confidence * 0.9, ev)
                ))
        
        return recommendations
    
    def _analyze_total_maps_bets(self, match_name: str, prediction: Dict, 
                                confidence: float) -> List[BettingRecommendation]:
        """Analyze total maps betting opportunities"""
        recommendations = []
        
        # Simulate total maps analysis based on team styles
        team1_prob = prediction['team1_win_probability']
        
        # If match is close, more likely to go to more maps
        if 0.4 <= team1_prob <= 0.6:
            over_25_prob = 0.7  # Close match likely to go over 2.5 maps
            over_odds = 1 / over_25_prob * 1.1
            ev = self._calculate_expected_value(over_25_prob, over_odds)
            
            if ev > self.min_ev:
                stake = self._calculate_kelly_stake(over_25_prob, over_odds)
                recommendations.append(BettingRecommendation(
                    bet_type="Total Maps",
                    match=match_name,
                    recommendation="Over 2.5 maps",
                    confidence=confidence * 0.85,
                    expected_value=ev,
                    reasoning="Close matchup between evenly matched teams likely to extend to multiple maps.",
                    suggested_stake=stake,
                    odds=over_odds,
                    risk_level=self._assess_risk_level(confidence * 0.85, ev)
                ))
        
        return recommendations
    
    def _analyze_first_map_bets(self, match_name: str, team1: str, team2: str,
                               prediction: Dict, confidence: float) -> List[BettingRecommendation]:
        """Analyze first map betting opportunities"""
        recommendations = []
        
        # First map often has different dynamics
        team1_prob = prediction['team1_win_probability']
        
        # Adjust probability for first map (often more volatile)
        first_map_prob = 0.5 + (team1_prob - 0.5) * 0.7  # Compress towards 50%
        
        if first_map_prob > 0.6:
            first_map_odds = 1 / first_map_prob * 1.1
            ev = self._calculate_expected_value(first_map_prob, first_map_odds)
            
            if ev > self.min_ev:
                stake = self._calculate_kelly_stake(first_map_prob, first_map_odds)
                recommendations.append(BettingRecommendation(
                    bet_type="First Map Winner",
                    match=match_name,
                    recommendation=f"{team1} to win first map",
                    confidence=confidence * 0.8,
                    expected_value=ev,
                    reasoning=f"{team1} has strong opening map performance and favorable map pool position.",
                    suggested_stake=stake,
                    odds=first_map_odds,
                    risk_level=self._assess_risk_level(confidence * 0.8, ev)
                ))
        
        return recommendations
    
    def _estimate_market_odds(self, team1_prob: float, team2_prob: float) -> Dict:
        """Estimate market odds based on probabilities"""
        # Add bookmaker margin (typically 5-10%)
        margin = 1.08
        
        team1_odds = (1 / team1_prob) * margin
        team2_odds = (1 / team2_prob) * margin
        
        return {
            'team1_win': team1_odds,
            'team2_win': team2_odds
        }
    
    def _calculate_expected_value(self, probability: float, odds: float) -> float:
        """Calculate expected value of a bet"""
        return (probability * (odds - 1)) - (1 - probability)
    
    def _calculate_kelly_stake(self, probability: float, odds: float) -> float:
        """Calculate optimal stake using Kelly Criterion"""
        edge = probability - (1 / odds)
        if edge <= 0:
            return 0.0
        
        kelly_fraction = edge / (odds - 1)
        
        # Apply conservative Kelly fraction and maximum bet size limits
        conservative_kelly = kelly_fraction * self.kelly_fraction
        return min(conservative_kelly, self.max_bet_size)
    
    def _assess_risk_level(self, confidence: float, expected_value: float) -> str:
        """Assess risk level of a bet"""
        if confidence >= 0.8 and expected_value >= 0.1:
            return "Low"
        elif confidence >= 0.7 and expected_value >= 0.05:
            return "Medium"
        else:
            return "High"
    
    def calculate_portfolio_risk(self, recommendations: List[BettingRecommendation]) -> Dict:
        """Calculate overall portfolio risk and correlation"""
        if not recommendations:
            return {'total_risk': 0.0, 'diversification_score': 1.0}
        
        # Calculate total stake
        total_stake = sum(rec.suggested_stake for rec in recommendations)
        
        # Calculate risk concentration
        max_single_bet = max(rec.suggested_stake for rec in recommendations)
        concentration_risk = max_single_bet / total_stake if total_stake > 0 else 0
        
        # Calculate bet type diversification
        bet_types = set(rec.bet_type for rec in recommendations)
        diversification_score = len(bet_types) / len(config.betting.bet_types)
        
        return {
            'total_stake': total_stake,
            'concentration_risk': concentration_risk,
            'diversification_score': diversification_score,
            'total_risk': concentration_risk * (1 - diversification_score)
        }
