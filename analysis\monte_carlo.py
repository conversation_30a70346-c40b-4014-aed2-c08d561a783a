"""
Monte Carlo Simulation Engine for CS2 Match Predictions
"""
import numpy as np
import random
from typing import Dict, List, Tuple
from config import config
from utils.logger import logger

class MonteCarloSimulator:
    """Monte Carlo simulation engine for match outcome predictions"""
    
    def __init__(self, num_simulations: int = None):
        self.num_simulations = num_simulations or config.prediction.monte_carlo_simulations
        
    def simulate_match(self, team1_prob: float, team2_prob: float, 
                      match_format: str = "bo3") -> Dict:
        """
        Simulate a match outcome using Monte Carlo method
        
        Args:
            team1_prob: Team 1 win probability for individual maps
            team2_prob: Team 2 win probability for individual maps
            match_format: Match format (bo1, bo3, bo5)
        
        Returns:
            Dictionary with simulation results
        """
        logger.debug(f"Running {self.num_simulations} simulations for {match_format} match")
        
        # Parse match format
        maps_to_win = self._parse_match_format(match_format)
        
        results = {
            'team1_wins': 0,
            'team2_wins': 0,
            'score_distribution': {},
            'confidence_interval': {},
            'upset_probability': 0.0
        }
        
        # Run simulations
        for _ in range(self.num_simulations):
            winner, score = self._simulate_single_match(team1_prob, team2_prob, maps_to_win)
            
            if winner == 1:
                results['team1_wins'] += 1
            else:
                results['team2_wins'] += 1
            
            # Track score distribution
            score_key = f"{score[0]}-{score[1]}"
            results['score_distribution'][score_key] = results['score_distribution'].get(score_key, 0) + 1
        
        # Calculate final probabilities
        results['team1_win_probability'] = results['team1_wins'] / self.num_simulations
        results['team2_win_probability'] = results['team2_wins'] / self.num_simulations
        
        # Calculate confidence intervals
        results['confidence_interval'] = self._calculate_confidence_interval(
            results['team1_wins'], self.num_simulations
        )
        
        # Calculate upset probability (lower probability team winning)
        expected_winner = 1 if team1_prob > team2_prob else 2
        if expected_winner == 1:
            results['upset_probability'] = results['team2_win_probability']
        else:
            results['upset_probability'] = results['team1_win_probability']
        
        return results
    
    def simulate_map_outcomes(self, team1_prob: float, team2_prob: float,
                            num_maps: int = 3) -> Dict:
        """Simulate individual map outcomes"""
        map_results = []
        
        for _ in range(self.num_simulations):
            maps = []
            for map_num in range(num_maps):
                # Add some variance to map probabilities
                variance = np.random.normal(0, 0.05)  # 5% standard deviation
                adjusted_prob1 = max(0.1, min(0.9, team1_prob + variance))
                
                if random.random() < adjusted_prob1:
                    maps.append(1)  # Team 1 wins
                else:
                    maps.append(2)  # Team 2 wins
            
            map_results.append(maps)
        
        # Analyze map-by-map results
        map_analysis = {}
        for map_idx in range(num_maps):
            team1_wins = sum(1 for result in map_results if result[map_idx] == 1)
            map_analysis[f'map_{map_idx + 1}'] = {
                'team1_win_probability': team1_wins / self.num_simulations,
                'team2_win_probability': 1 - (team1_wins / self.num_simulations)
            }
        
        return map_analysis
    
    def simulate_tournament_bracket(self, teams_probabilities: Dict) -> Dict:
        """Simulate tournament bracket outcomes"""
        # This would be used for tournament predictions
        # Simplified implementation for now
        return {}
    
    def _parse_match_format(self, match_format: str) -> int:
        """Parse match format to determine maps needed to win"""
        format_map = {
            'bo1': 1,
            'bo3': 2,
            'bo5': 3,
            'best of 1': 1,
            'best of 3': 2,
            'best of 5': 3
        }
        
        return format_map.get(match_format.lower(), 2)  # Default to bo3
    
    def _simulate_single_match(self, team1_prob: float, team2_prob: float, 
                              maps_to_win: int) -> Tuple[int, Tuple[int, int]]:
        """Simulate a single match"""
        team1_score = 0
        team2_score = 0
        
        while team1_score < maps_to_win and team2_score < maps_to_win:
            # Simulate individual map with some variance
            variance = np.random.normal(0, 0.03)  # 3% standard deviation
            adjusted_prob1 = max(0.05, min(0.95, team1_prob + variance))
            
            if random.random() < adjusted_prob1:
                team1_score += 1
            else:
                team2_score += 1
        
        winner = 1 if team1_score > team2_score else 2
        return winner, (team1_score, team2_score)
    
    def _calculate_confidence_interval(self, successes: int, trials: int, 
                                     confidence_level: float = 0.95) -> Dict:
        """Calculate confidence interval for win probability"""
        p = successes / trials
        z_score = 1.96 if confidence_level == 0.95 else 2.576  # 95% or 99%
        
        margin_of_error = z_score * np.sqrt((p * (1 - p)) / trials)
        
        return {
            'lower_bound': max(0, p - margin_of_error),
            'upper_bound': min(1, p + margin_of_error),
            'margin_of_error': margin_of_error
        }
    
    def analyze_variance(self, base_prob: float, variance_scenarios: List[float]) -> Dict:
        """Analyze how variance affects outcomes"""
        variance_analysis = {}
        
        for variance in variance_scenarios:
            results = []
            for _ in range(1000):  # Smaller sample for variance analysis
                adjusted_prob = max(0.1, min(0.9, base_prob + np.random.normal(0, variance)))
                winner, _ = self._simulate_single_match(adjusted_prob, 1 - adjusted_prob, 2)
                results.append(winner)
            
            team1_wins = sum(1 for r in results if r == 1)
            variance_analysis[f'variance_{variance:.2f}'] = {
                'team1_win_rate': team1_wins / len(results),
                'stability': 1 - abs((team1_wins / len(results)) - base_prob)
            }
        
        return variance_analysis
    
    def simulate_betting_scenarios(self, match_prob: float, odds: Dict) -> Dict:
        """Simulate betting scenarios with different stake sizes"""
        betting_results = {}
        
        # Kelly Criterion calculation
        for bet_type, odd in odds.items():
            implied_prob = 1 / odd
            edge = match_prob - implied_prob
            
            if edge > 0:  # Positive expected value
                kelly_fraction = edge / (odd - 1)
                
                # Simulate betting outcomes
                wins = 0
                total_return = 0
                
                for _ in range(self.num_simulations):
                    if random.random() < match_prob:
                        wins += 1
                        total_return += odd - 1  # Profit
                    else:
                        total_return -= 1  # Loss
                
                betting_results[bet_type] = {
                    'win_rate': wins / self.num_simulations,
                    'expected_return': total_return / self.num_simulations,
                    'kelly_fraction': kelly_fraction,
                    'recommended_stake': min(kelly_fraction * config.betting.kelly_fraction, 
                                           config.betting.max_bet_size)
                }
        
        return betting_results
    
    def stress_test_predictions(self, base_team1_prob: float, 
                               stress_factors: Dict) -> Dict:
        """Stress test predictions under different scenarios"""
        stress_results = {}
        
        for scenario, factor in stress_factors.items():
            # Apply stress factor to base probability
            stressed_prob = max(0.1, min(0.9, base_team1_prob * factor))
            
            # Run simulation with stressed probability
            results = self.simulate_match(stressed_prob, 1 - stressed_prob)
            
            stress_results[scenario] = {
                'adjusted_probability': stressed_prob,
                'team1_win_rate': results['team1_win_probability'],
                'confidence_change': abs(results['team1_win_probability'] - base_team1_prob),
                'upset_probability': results['upset_probability']
            }
        
        return stress_results
