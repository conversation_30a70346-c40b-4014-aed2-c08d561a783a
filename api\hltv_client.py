"""
HLTV API Client for CS2 Betting Prediction System
"""
import requests
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
from config import config
from utils.logger import logger

class HLTVClient:
    """Client for interacting with the local HLTV API server"""
    
    def __init__(self):
        self.base_url = config.api.hltv_api_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CS2-Betting-Predictor/1.0',
            'Accept': 'application/json'
        })
        self.last_request_time = 0
        self.rate_limit_delay = 60 / config.api.hltv_rate_limit  # seconds between requests
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make a request to the HLTV API with error handling and retries"""
        self._rate_limit()
        
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(config.api.max_retries):
            try:
                start_time = time.time()
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=config.api.timeout
                )
                response_time = time.time() - start_time
                
                logger.log_api_call(endpoint, response.status_code, response_time)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limited
                    logger.warning(f"Rate limited on {endpoint}, waiting...")
                    time.sleep(config.api.retry_delay * (attempt + 1))
                    continue
                else:
                    logger.error(f"HTTP {response.status_code} for {endpoint}: {response.text}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request failed for {endpoint}", e)
                if attempt < config.api.max_retries - 1:
                    time.sleep(config.api.retry_delay * (attempt + 1))
                    continue
                return None
        
        return None
    
    def get_upcoming_matches(self, limit: int = 50) -> List[Dict]:
        """Get upcoming matches"""
        data = self._make_request("/matches/upcoming", {"limit": limit})
        return data.get("matches", []) if data else []
    
    def get_match_details(self, match_id: int) -> Optional[Dict]:
        """Get detailed information about a specific match"""
        return self._make_request(f"/matches/{match_id}")
    
    def get_match_stats(self, match_id: int) -> Optional[Dict]:
        """Get detailed statistics for a match"""
        return self._make_request(f"/matches/{match_id}/stats")
    
    def get_team_info(self, team_id: int) -> Optional[Dict]:
        """Get team information"""
        return self._make_request(f"/teams/{team_id}")
    
    def get_team_stats(self, team_id: int) -> Optional[Dict]:
        """Get team statistics"""
        return self._make_request(f"/teams/stats/{team_id}")
    
    def get_head_to_head(self, team1_id: int, team2_id: int) -> Optional[Dict]:
        """Get head-to-head statistics between two teams"""
        return self._make_request(f"/teams/{team1_id}/vs/{team2_id}")
    
    def get_team_by_name(self, team_name: str) -> Optional[Dict]:
        """Get team information by name"""
        return self._make_request(f"/teams/name/{team_name}")
    
    def search_teams(self, query: str) -> List[Dict]:
        """Search for teams by name"""
        data = self._make_request("/teams/search", {"query": query})
        return data.get("teams", []) if data else []
    
    def get_player_info(self, player_id: int) -> Optional[Dict]:
        """Get player information"""
        return self._make_request(f"/players/{player_id}")
    
    def get_player_stats(self, player_id: int) -> Optional[Dict]:
        """Get player statistics"""
        return self._make_request(f"/players/stats/{player_id}")
    
    def search_players(self, query: str) -> List[Dict]:
        """Search for players by name"""
        data = self._make_request("/players/search", {"query": query})
        return data.get("players", []) if data else []
    
    def get_events(self) -> List[Dict]:
        """Get all events"""
        data = self._make_request("/events")
        return data.get("events", []) if data else []
    
    def get_event_info(self, event_id: int) -> Optional[Dict]:
        """Get event information"""
        return self._make_request(f"/events/{event_id}")
    
    def get_upcoming_events(self, limit: int = 20) -> List[Dict]:
        """Get upcoming events"""
        data = self._make_request("/events/upcoming", {"limit": limit})
        return data.get("events", []) if data else []
    
    def get_results(self, event_ids: List[int], best_of_x: int) -> List[Dict]:
        """Get match results filtered by event IDs and format"""
        params = {
            "eventIds": json.dumps(event_ids),
            "bestOfX": best_of_x
        }
        data = self._make_request("/results", params)
        return data.get("results", []) if data else []
    
    def get_team_rankings(self) -> List[Dict]:
        """Get team rankings"""
        data = self._make_request("/ranking/teams")
        return data.get("rankings", []) if data else []
    
    def get_player_rankings(self) -> List[Dict]:
        """Get player rankings"""
        data = self._make_request("/ranking/players")
        return data.get("rankings", []) if data else []
    
    def get_matches_by_date(self, date: str) -> Dict:
        """Get matches for a specific date (YYYY-MM-DD format)"""
        return self._make_request("/matches", {"selectedDate": date})
    
    def get_live_matches(self) -> List[Dict]:
        """Get currently live matches"""
        data = self._make_request("/matches/live")
        return data.get("matches", []) if data else []
    
    def get_news(self) -> List[Dict]:
        """Get latest news articles"""
        data = self._make_request("/news")
        return data.get("articles", []) if data else []
    
    def health_check(self) -> bool:
        """Check if the HLTV API server is responding"""
        try:
            response = self.session.get(f"{self.base_url}/events", timeout=5)
            return response.status_code == 200
        except:
            return False
