"""
Main CS2 Betting Prediction System
"""
import sys
import os
from typing import Dict, List
from datetime import datetime
import json

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_collector import DataCollector
from analysis.statistical_engine import StatisticalEngine
from analysis.monte_carlo import MonteCarloSimulator
from betting.strategy_engine import BettingStrategyEngine, BettingRecommendation
from config import config
from utils.logger import logger

class CS2BettingPredictor:
    """Main prediction system class"""
    
    def __init__(self):
        self.data_collector = DataCollector()
        self.statistical_engine = StatisticalEngine()
        self.monte_carlo = MonteCarloSimulator()
        self.betting_engine = BettingStrategyEngine()
        
        logger.info("CS2 Betting Prediction System initialized")
    
    def run_full_analysis(self) -> Dict:
        """Run complete analysis and generate betting recommendations"""
        logger.info("Starting full analysis for CS2 betting predictions")
        
        try:
            # Step 1: Collect match data for target date
            logger.info("Step 1: Collecting match data...")
            target_matches = self.data_collector.collect_target_date_matches()
            
            if not target_matches:
                logger.error("No matches found for target date")
                return {'error': 'No matches found for target date'}
            
            logger.info(f"Found {len(target_matches)} matches for analysis")
            
            # Step 2: Extract team names and collect team data
            logger.info("Step 2: Collecting team data...")
            team_names = self._extract_team_names(target_matches)
            team_data = self.data_collector.collect_team_data(team_names)
            
            # Step 3: Collect historical data for context
            logger.info("Step 3: Collecting historical data...")
            historical_data = self.data_collector.collect_historical_data()
            
            # Step 4: Analyze matches and generate predictions
            logger.info("Step 4: Generating match predictions...")
            match_predictions = self._analyze_matches(target_matches, team_data)
            
            # Step 5: Generate betting recommendations
            logger.info("Step 5: Generating betting recommendations...")
            recommendations = self.betting_engine.generate_recommendations(match_predictions)
            
            # Step 6: Compile final results
            results = {
                'analysis_date': datetime.now().isoformat(),
                'target_date': config.get_target_date_str(),
                'total_matches_analyzed': len(target_matches),
                'teams_analyzed': len(team_names),
                'recommendations': self._format_recommendations(recommendations),
                'match_predictions': match_predictions,
                'portfolio_analysis': self.betting_engine.calculate_portfolio_risk(recommendations),
                'system_confidence': self._calculate_system_confidence(match_predictions)
            }
            
            # Save results
            self._save_results(results)
            
            logger.info("Analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error("Error during analysis", e)
            return {'error': str(e)}
    
    def _extract_team_names(self, matches: List[Dict]) -> List[str]:
        """Extract unique team names from matches"""
        team_names = set()
        
        for match in matches:
            if match.get('source') == 'pandascore':
                opponents = match.get('opponents', [])
                for opponent in opponents:
                    team_name = opponent.get('opponent', {}).get('name')
                    if team_name:
                        team_names.add(team_name)
            elif match.get('source') == 'hltv':
                team1 = match.get('team1', {}).get('name')
                team2 = match.get('team2', {}).get('name')
                if team1:
                    team_names.add(team1)
                if team2:
                    team_names.add(team2)
        
        return list(team_names)
    
    def _analyze_matches(self, matches: List[Dict], team_data: Dict) -> List[Dict]:
        """Analyze each match and generate predictions"""
        predictions = []
        
        for match in matches:
            try:
                # Extract team names based on source
                if match.get('source') == 'pandascore':
                    opponents = match.get('opponents', [])
                    if len(opponents) >= 2:
                        team1_name = opponents[0].get('opponent', {}).get('name')
                        team2_name = opponents[1].get('opponent', {}).get('name')
                    else:
                        continue
                elif match.get('source') == 'hltv':
                    team1_name = match.get('team1', {}).get('name')
                    team2_name = match.get('team2', {}).get('name')
                else:
                    continue
                
                if not team1_name or not team2_name:
                    continue
                
                # Get team data
                team1_data = team_data.get(team1_name, {'name': team1_name})
                team2_data = team_data.get(team2_name, {'name': team2_name})
                
                # Generate statistical prediction
                stat_prediction = self.statistical_engine.predict_match_outcome(team1_data, team2_data)
                
                # Run Monte Carlo simulation
                match_format = match.get('format', 'bo3')
                mc_results = self.monte_carlo.simulate_match(
                    stat_prediction['team1_win_probability'],
                    stat_prediction['team2_win_probability'],
                    match_format
                )
                
                # Combine predictions
                combined_prediction = {
                    'match_info': {
                        'team1': team1_name,
                        'team2': team2_name,
                        'format': match_format,
                        'date': match.get('begin_at') or match.get('date'),
                        'tournament': match.get('tournament', {}).get('name') or match.get('event', {}).get('name'),
                        'source': match.get('source')
                    },
                    'team1_win_probability': mc_results['team1_win_probability'],
                    'team2_win_probability': mc_results['team2_win_probability'],
                    'confidence': stat_prediction['confidence'],
                    'statistical_analysis': stat_prediction,
                    'monte_carlo_results': mc_results,
                    'upset_probability': mc_results['upset_probability']
                }
                
                predictions.append(combined_prediction)
                
                logger.info(f"Analyzed match: {team1_name} vs {team2_name} - Confidence: {stat_prediction['confidence']:.2%}")
                
            except Exception as e:
                logger.error(f"Error analyzing match: {match}", e)
                continue
        
        return predictions
    
    def _format_recommendations(self, recommendations: List[BettingRecommendation]) -> List[Dict]:
        """Format recommendations for output"""
        formatted = []
        
        for i, rec in enumerate(recommendations, 1):
            formatted.append({
                'bet_number': i,
                'bet_type': rec.bet_type,
                'match': rec.match,
                'recommendation': rec.recommendation,
                'confidence': f"{rec.confidence:.1%}",
                'expected_value': f"{rec.expected_value:.1%}",
                'reasoning': rec.reasoning,
                'suggested_stake': f"{rec.suggested_stake:.1%}",
                'risk_level': rec.risk_level,
                'odds': rec.odds
            })
        
        return formatted
    
    def _calculate_system_confidence(self, predictions: List[Dict]) -> float:
        """Calculate overall system confidence"""
        if not predictions:
            return 0.0
        
        confidences = [pred['confidence'] for pred in predictions]
        return sum(confidences) / len(confidences)
    
    def _save_results(self, results: Dict) -> None:
        """Save results to file"""
        try:
            output_file = os.path.join(
                config.data.output_dir, 
                f"cs2_betting_predictions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Results saved to {output_file}")
            
        except Exception as e:
            logger.error("Error saving results", e)
    
    def print_recommendations(self, results: Dict) -> None:
        """Print formatted recommendations to console"""
        if 'error' in results:
            print(f"❌ Error: {results['error']}")
            return
        
        recommendations = results.get('recommendations', [])
        
        print("\n" + "="*80)
        print("🎯 CS2 BETTING PREDICTIONS - TOP 5 RECOMMENDATIONS")
        print(f"📅 Target Date: {results['target_date']}")
        print(f"🔍 Matches Analyzed: {results['total_matches_analyzed']}")
        print(f"📊 System Confidence: {results['system_confidence']:.1%}")
        print("="*80)
        
        if not recommendations:
            print("\n❌ No high-confidence recommendations found for the target date.")
            print("   This could be due to:")
            print("   - Insufficient data for reliable predictions")
            print("   - No matches meeting minimum confidence threshold (70%)")
            print("   - Limited match availability for the target date")
            return
        
        for rec in recommendations:
            print(f"\n🎲 Bet #{rec['bet_number']}: {rec['bet_type']}")
            print(f"⚽ Match: {rec['match']}")
            print(f"💡 Recommendation: {rec['recommendation']}")
            print(f"🎯 Confidence: {rec['confidence']}")
            print(f"💰 Expected Value: {rec['expected_value']}")
            print(f"📈 Suggested Stake: {rec['suggested_stake']} of bankroll")
            print(f"⚠️  Risk Level: {rec['risk_level']}")
            print(f"🧠 Reasoning: {rec['reasoning']}")
            if rec.get('odds'):
                print(f"📊 Estimated Odds: {rec['odds']:.2f}")
            print("-" * 60)
        
        # Portfolio analysis
        portfolio = results.get('portfolio_analysis', {})
        print(f"\n📊 PORTFOLIO ANALYSIS")
        print(f"💼 Total Stake: {portfolio.get('total_stake', 0):.1%} of bankroll")
        print(f"🎯 Diversification Score: {portfolio.get('diversification_score', 0):.1%}")
        print(f"⚠️  Portfolio Risk: {portfolio.get('total_risk', 0):.1%}")
        
        print("\n" + "="*80)
        print("⚠️  DISCLAIMER: These are predictions based on statistical analysis.")
        print("   Always gamble responsibly and never bet more than you can afford to lose.")
        print("="*80)

def main():
    """Main function to run the prediction system"""
    print("🚀 Starting CS2 Betting Prediction System...")
    
    predictor = CS2BettingPredictor()
    results = predictor.run_full_analysis()
    predictor.print_recommendations(results)

if __name__ == "__main__":
    main()
