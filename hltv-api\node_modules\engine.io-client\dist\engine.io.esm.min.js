/*!
 * Engine.IO v6.6.3
 * (c) 2014-2025 <PERSON>
 * Released under the MIT License.
 */
const t=Object.create(null);t.open="0",t.close="1",t.ping="2",t.pong="3",t.message="4",t.upgrade="5",t.noop="6";const s=Object.create(null);Object.keys(t).forEach((e=>{s[t[e]]=e}));const e={type:"error",data:"parser error"},i="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),n="function"==typeof ArrayBuffer,r=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,o=({type:s,data:e},o,c)=>i&&e instanceof Blob?o?c(e):h(e,c):n&&(e instanceof ArrayBuffer||r(e))?o?c(e):h(new Blob([e]),c):c(t[s]+(e||"")),h=(t,s)=>{const e=new FileReader;return e.onload=function(){const t=e.result.split(",")[1];s("b"+(t||""))},e.readAsDataURL(t)};function c(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let a;const u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<64;t++)f[u.charCodeAt(t)]=t;const l="function"==typeof ArrayBuffer,p=(t,i)=>{if("string"!=typeof t)return{type:"message",data:y(t,i)};const n=t.charAt(0);if("b"===n)return{type:"message",data:d(t.substring(1),i)};return s[n]?t.length>1?{type:s[n],data:t.substring(1)}:{type:s[n]}:e},d=(t,s)=>{if(l){const e=(t=>{let s,e,i,n,r,o=.75*t.length,h=t.length,c=0;"="===t[t.length-1]&&(o--,"="===t[t.length-2]&&o--);const a=new ArrayBuffer(o),u=new Uint8Array(a);for(s=0;s<h;s+=4)e=f[t.charCodeAt(s)],i=f[t.charCodeAt(s+1)],n=f[t.charCodeAt(s+2)],r=f[t.charCodeAt(s+3)],u[c++]=e<<2|i>>4,u[c++]=(15&i)<<4|n>>2,u[c++]=(3&n)<<6|63&r;return a})(t);return y(e,s)}return{base64:!0,data:t}},y=(t,s)=>"blob"===s?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer,g=String.fromCharCode(30);function w(){return new TransformStream({transform(t,s){!function(t,s){i&&t.data instanceof Blob?t.data.arrayBuffer().then(c).then(s):n&&(t.data instanceof ArrayBuffer||r(t.data))?s(c(t.data)):o(t,!1,(t=>{a||(a=new TextEncoder),s(a.encode(t))}))}(t,(e=>{const i=e.length;let n;if(i<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,i);else if(i<65536){n=new Uint8Array(3);const t=new DataView(n.buffer);t.setUint8(0,126),t.setUint16(1,i)}else{n=new Uint8Array(9);const t=new DataView(n.buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(i))}t.data&&"string"!=typeof t.data&&(n[0]|=128),s.enqueue(n),s.enqueue(e)}))}})}let b;function m(t){return t.reduce(((t,s)=>t+s.length),0)}function v(t,s){if(t[0].length===s)return t.shift();const e=new Uint8Array(s);let i=0;for(let n=0;n<s;n++)e[n]=t[0][i++],i===t[0].length&&(t.shift(),i=0);return t.length&&i<t[0].length&&(t[0]=t[0].slice(i)),e}function k(t){if(t)return function(t){for(var s in k.prototype)t[s]=k.prototype[s];return t}(t)}k.prototype.on=k.prototype.addEventListener=function(t,s){return this.t=this.t||{},(this.t["$"+t]=this.t["$"+t]||[]).push(s),this},k.prototype.once=function(t,s){function e(){this.off(t,e),s.apply(this,arguments)}return e.fn=s,this.on(t,e),this},k.prototype.off=k.prototype.removeListener=k.prototype.removeAllListeners=k.prototype.removeEventListener=function(t,s){if(this.t=this.t||{},0==arguments.length)return this.t={},this;var e,i=this.t["$"+t];if(!i)return this;if(1==arguments.length)return delete this.t["$"+t],this;for(var n=0;n<i.length;n++)if((e=i[n])===s||e.fn===s){i.splice(n,1);break}return 0===i.length&&delete this.t["$"+t],this},k.prototype.emit=function(t){this.t=this.t||{};for(var s=new Array(arguments.length-1),e=this.t["$"+t],i=1;i<arguments.length;i++)s[i-1]=arguments[i];if(e){i=0;for(var n=(e=e.slice(0)).length;i<n;++i)e[i].apply(this,s)}return this},k.prototype.emitReserved=k.prototype.emit,k.prototype.listeners=function(t){return this.t=this.t||{},this.t["$"+t]||[]},k.prototype.hasListeners=function(t){return!!this.listeners(t).length};const x="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,s)=>s(t,0),E="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function A(t,...s){return s.reduce(((s,e)=>(t.hasOwnProperty(e)&&(s[e]=t[e]),s)),{})}const U=E.setTimeout,B=E.clearTimeout;function O(t,s){s.useNativeTimers?(t.setTimeoutFn=U.bind(E),t.clearTimeoutFn=B.bind(E)):(t.setTimeoutFn=E.setTimeout.bind(E),t.clearTimeoutFn=E.clearTimeout.bind(E))}function T(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class _ extends Error{constructor(t,s,e){super(t),this.description=s,this.context=e,this.type="TransportError"}}class C extends k{constructor(t){super(),this.writable=!1,O(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,s,e){return super.emitReserved("error",new _(t,s,e)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const s=p(t,this.socket.binaryType);this.onPacket(s)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,s={}){return t+"://"+this.i()+this.o()+this.opts.path+this.h(s)}i(){const t=this.opts.hostname;return-1===t.indexOf(":")?t:"["+t+"]"}o(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}h(t){const s=function(t){let s="";for(let e in t)t.hasOwnProperty(e)&&(s.length&&(s+="&"),s+=encodeURIComponent(e)+"="+encodeURIComponent(t[e]));return s}(t);return s.length?"?"+s:""}}class P extends C{constructor(){super(...arguments),this.u=!1}get name(){return"polling"}doOpen(){this.l()}pause(t){this.readyState="pausing";const s=()=>{this.readyState="paused",t()};if(this.u||!this.writable){let t=0;this.u&&(t++,this.once("pollComplete",(function(){--t||s()}))),this.writable||(t++,this.once("drain",(function(){--t||s()})))}else s()}l(){this.u=!0,this.doPoll(),this.emitReserved("poll")}onData(t){((t,s)=>{const e=t.split(g),i=[];for(let t=0;t<e.length;t++){const n=p(e[t],s);if(i.push(n),"error"===n.type)break}return i})(t,this.socket.binaryType).forEach((t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)})),"closed"!==this.readyState&&(this.u=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.l())}doClose(){const t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,((t,s)=>{const e=t.length,i=new Array(e);let n=0;t.forEach(((t,r)=>{o(t,!1,(t=>{i[r]=t,++n===e&&s(i.join(g))}))}))})(t,(t=>{this.doWrite(t,(()=>{this.writable=!0,this.emitReserved("drain")}))}))}uri(){const t=this.opts.secure?"https":"http",s=this.query||{};return!1!==this.opts.timestampRequests&&(s[this.opts.timestampParam]=T()),this.supportsBinary||s.sid||(s.b64=1),this.createUri(t,s)}}let j=!1;try{j="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}const D=j;function L(){}class M extends P{constructor(t){if(super(t),"undefined"!=typeof location){const s="https:"===location.protocol;let e=location.port;e||(e=s?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||e!==t.port}}doWrite(t,s){const e=this.request({method:"POST",data:t});e.on("success",s),e.on("error",((t,s)=>{this.onError("xhr post error",t,s)}))}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",((t,s)=>{this.onError("xhr poll error",t,s)})),this.pollXhr=t}}class S extends k{constructor(t,s,e){super(),this.createRequest=t,O(this,e),this.p=e,this.m=e.method||"GET",this.v=s,this.k=void 0!==e.data?e.data:null,this.A()}A(){var t;const s=A(this.p,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");s.xdomain=!!this.p.xd;const e=this.U=this.createRequest(s);try{e.open(this.m,this.v,!0);try{if(this.p.extraHeaders){e.setDisableHeaderCheck&&e.setDisableHeaderCheck(!0);for(let t in this.p.extraHeaders)this.p.extraHeaders.hasOwnProperty(t)&&e.setRequestHeader(t,this.p.extraHeaders[t])}}catch(t){}if("POST"===this.m)try{e.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{e.setRequestHeader("Accept","*/*")}catch(t){}null===(t=this.p.cookieJar)||void 0===t||t.addCookies(e),"withCredentials"in e&&(e.withCredentials=this.p.withCredentials),this.p.requestTimeout&&(e.timeout=this.p.requestTimeout),e.onreadystatechange=()=>{var t;3===e.readyState&&(null===(t=this.p.cookieJar)||void 0===t||t.parseCookies(e.getResponseHeader("set-cookie"))),4===e.readyState&&(200===e.status||1223===e.status?this.B():this.setTimeoutFn((()=>{this.O("number"==typeof e.status?e.status:0)}),0))},e.send(this.k)}catch(t){return void this.setTimeoutFn((()=>{this.O(t)}),0)}"undefined"!=typeof document&&(this.T=S.requestsCount++,S.requests[this.T]=this)}O(t){this.emitReserved("error",t,this.U),this._(!0)}_(t){if(void 0!==this.U&&null!==this.U){if(this.U.onreadystatechange=L,t)try{this.U.abort()}catch(t){}"undefined"!=typeof document&&delete S.requests[this.T],this.U=null}}B(){const t=this.U.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._())}abort(){this._()}}if(S.requestsCount=0,S.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",R);else if("function"==typeof addEventListener){addEventListener("onpagehide"in E?"pagehide":"unload",R,!1)}function R(){for(let t in S.requests)S.requests.hasOwnProperty(t)&&S.requests[t].abort()}const H=function(){const t=q({xdomain:!1});return t&&null!==t.responseType}();class $ extends M{constructor(t){super(t);const s=t&&t.forceBase64;this.supportsBinary=H&&!s}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new S(q,this.uri(),t)}}function q(t){const s=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!s||D))return new XMLHttpRequest}catch(t){}if(!s)try{return new(E[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}const I="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class W extends C{get name(){return"websocket"}doOpen(){const t=this.uri(),s=this.opts.protocols,e=I?{}:A(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(e.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,s,e)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws.C.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const e=t[s],i=s===t.length-1;o(e,this.supportsBinary,(t=>{try{this.doWrite(e,t)}catch(t){}i&&x((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=T()),this.supportsBinary||(s.b64=1),this.createUri(t,s)}}const N=E.WebSocket||E.MozWebSocket;class X extends W{createSocket(t,s,e){return I?new N(t,s,e):s?new N(t,s):new N(t)}doWrite(t,s){this.ws.send(s)}}class V extends C{get name(){return"webtransport"}doOpen(){try{this.P=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this.P.closed.then((()=>{this.onClose()})).catch((t=>{this.onError("webtransport error",t)})),this.P.ready.then((()=>{this.P.createBidirectionalStream().then((t=>{const s=function(t,s){b||(b=new TextDecoder);const i=[];let n=0,r=-1,o=!1;return new TransformStream({transform(h,c){for(i.push(h);;){if(0===n){if(m(i)<1)break;const t=v(i,1);o=!(128&~t[0]),r=127&t[0],n=r<126?3:126===r?1:2}else if(1===n){if(m(i)<2)break;const t=v(i,2);r=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),n=3}else if(2===n){if(m(i)<8)break;const t=v(i,8),s=new DataView(t.buffer,t.byteOffset,t.length),o=s.getUint32(0);if(o>Math.pow(2,21)-1){c.enqueue(e);break}r=o*Math.pow(2,32)+s.getUint32(4),n=3}else{if(m(i)<r)break;const t=v(i,r);c.enqueue(p(o?t:b.decode(t),s)),n=0}if(0===r||r>t){c.enqueue(e);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),i=t.readable.pipeThrough(s).getReader(),n=w();n.readable.pipeTo(t.writable),this.j=n.writable.getWriter();const r=()=>{i.read().then((({done:t,value:s})=>{t||(this.onPacket(s),r())})).catch((t=>{}))};r();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this.j.write(o).then((()=>this.onOpen()))}))}))}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const e=t[s],i=s===t.length-1;this.j.write(e).then((()=>{i&&x((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){var t;null===(t=this.P)||void 0===t||t.close()}}const F={websocket:X,webtransport:V,polling:$},z=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,G=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function J(t){if(t.length>8e3)throw"URI too long";const s=t,e=t.indexOf("["),i=t.indexOf("]");-1!=e&&-1!=i&&(t=t.substring(0,e)+t.substring(e,i).replace(/:/g,";")+t.substring(i,t.length));let n=z.exec(t||""),r={},o=14;for(;o--;)r[G[o]]=n[o]||"";return-1!=e&&-1!=i&&(r.source=s,r.host=r.host.substring(1,r.host.length-1).replace(/;/g,":"),r.authority=r.authority.replace("[","").replace("]","").replace(/;/g,":"),r.ipv6uri=!0),r.pathNames=function(t,s){const e=/\/{2,9}/g,i=s.replace(e,"/").split("/");"/"!=s.slice(0,1)&&0!==s.length||i.splice(0,1);"/"==s.slice(-1)&&i.splice(i.length-1,1);return i}(0,r.path),r.queryKey=function(t,s){const e={};return s.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(t,s,i){s&&(e[s]=i)})),e}(0,r.query),r}const K="function"==typeof addEventListener&&"function"==typeof removeEventListener,Q=[];K&&addEventListener("offline",(()=>{Q.forEach((t=>t()))}),!1);class Y extends k{constructor(t,s){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this.D=0,this.L=-1,this.M=-1,this.S=-1,this.R=1/0,t&&"object"==typeof t&&(s=t,t=null),t){const e=J(t);s.hostname=e.host,s.secure="https"===e.protocol||"wss"===e.protocol,s.port=e.port,e.query&&(s.query=e.query)}else s.host&&(s.hostname=J(s.host).host);O(this,s),this.secure=null!=s.secure?s.secure:"undefined"!=typeof location&&"https:"===location.protocol,s.hostname&&!s.port&&(s.port=this.secure?"443":"80"),this.hostname=s.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=s.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this.H={},s.transports.forEach((t=>{const s=t.prototype.name;this.transports.push(s),this.H[s]=t})),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let s={},e=t.split("&");for(let t=0,i=e.length;t<i;t++){let i=e[t].split("=");s[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return s}(this.opts.query)),K&&(this.opts.closeOnBeforeunload&&(this.$=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this.$,!1)),"localhost"!==this.hostname&&(this.q=()=>{this.I("transport close",{description:"network connection lost"})},Q.push(this.q))),this.opts.withCredentials&&(this.W=void 0),this.N()}createTransport(t){const s=Object.assign({},this.opts.query);s.EIO=4,s.transport=t,this.id&&(s.sid=this.id);const e=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this.H[t](e)}N(){if(0===this.transports.length)return void this.setTimeoutFn((()=>{this.emitReserved("error","No transports available")}),0);const t=this.opts.rememberUpgrade&&Y.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const s=this.createTransport(t);s.open(),this.setTransport(s)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.X.bind(this)).on("packet",this.V.bind(this)).on("error",this.O.bind(this)).on("close",(t=>this.I("transport close",t)))}onOpen(){this.readyState="open",Y.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}V(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.F("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this.G();break;case"error":const s=new Error("server error");s.code=t.data,this.O(s);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.L=t.pingInterval,this.M=t.pingTimeout,this.S=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.G()}G(){this.clearTimeoutFn(this.J);const t=this.L+this.M;this.R=Date.now()+t,this.J=this.setTimeoutFn((()=>{this.I("ping timeout")}),t),this.opts.autoUnref&&this.J.unref()}X(){this.writeBuffer.splice(0,this.D),this.D=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this.K();this.transport.send(t),this.D=t.length,this.emitReserved("flush")}}K(){if(!(this.S&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){const i=this.writeBuffer[e].data;if(i&&(t+="string"==typeof(s=i)?function(t){let s=0,e=0;for(let i=0,n=t.length;i<n;i++)s=t.charCodeAt(i),s<128?e+=1:s<2048?e+=2:s<55296||s>=57344?e+=3:(i++,e+=4);return e}(s):Math.ceil(1.33*(s.byteLength||s.size))),e>0&&t>this.S)return this.writeBuffer.slice(0,e);t+=2}var s;return this.writeBuffer}Y(){if(!this.R)return!0;const t=Date.now()>this.R;return t&&(this.R=0,x((()=>{this.I("ping timeout")}),this.setTimeoutFn)),t}write(t,s,e){return this.F("message",t,s,e),this}send(t,s,e){return this.F("message",t,s,e),this}F(t,s,e,i){if("function"==typeof s&&(i=s,s=void 0),"function"==typeof e&&(i=e,e=null),"closing"===this.readyState||"closed"===this.readyState)return;(e=e||{}).compress=!1!==e.compress;const n={type:t,data:s,options:e};this.emitReserved("packetCreate",n),this.writeBuffer.push(n),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this.I("forced close"),this.transport.close()},s=()=>{this.off("upgrade",s),this.off("upgradeError",s),t()},e=()=>{this.once("upgrade",s),this.once("upgradeError",s)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(()=>{this.upgrading?e():t()})):this.upgrading?e():t()),this}O(t){if(Y.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this.N();this.emitReserved("error",t),this.I("transport error",t)}I(t,s){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this.J),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),K&&(this.$&&removeEventListener("beforeunload",this.$,!1),this.q)){const t=Q.indexOf(this.q);-1!==t&&Q.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,s),this.writeBuffer=[],this.D=0}}}Y.protocol=4;class Z extends Y{constructor(){super(...arguments),this.Z=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this.Z.length;t++)this.tt(this.Z[t])}tt(t){let s=this.createTransport(t),e=!1;Y.priorWebsocketSuccess=!1;const i=()=>{e||(s.send([{type:"ping",data:"probe"}]),s.once("packet",(t=>{if(!e)if("pong"===t.type&&"probe"===t.data){if(this.upgrading=!0,this.emitReserved("upgrading",s),!s)return;Y.priorWebsocketSuccess="websocket"===s.name,this.transport.pause((()=>{e||"closed"!==this.readyState&&(a(),this.setTransport(s),s.send([{type:"upgrade"}]),this.emitReserved("upgrade",s),s=null,this.upgrading=!1,this.flush())}))}else{const t=new Error("probe error");t.transport=s.name,this.emitReserved("upgradeError",t)}})))};function n(){e||(e=!0,a(),s.close(),s=null)}const r=t=>{const e=new Error("probe error: "+t);e.transport=s.name,n(),this.emitReserved("upgradeError",e)};function o(){r("transport closed")}function h(){r("socket closed")}function c(t){s&&t.name!==s.name&&n()}const a=()=>{s.removeListener("open",i),s.removeListener("error",r),s.removeListener("close",o),this.off("close",h),this.off("upgrading",c)};s.once("open",i),s.once("error",r),s.once("close",o),this.once("close",h),this.once("upgrading",c),-1!==this.Z.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn((()=>{e||s.open()}),200):s.open()}onHandshake(t){this.Z=this.st(t.upgrades),super.onHandshake(t)}st(t){const s=[];for(let e=0;e<t.length;e++)~this.transports.indexOf(t[e])&&s.push(t[e]);return s}}class tt extends Z{constructor(t,s={}){const e="object"==typeof t?t:s;(!e.transports||e.transports&&"string"==typeof e.transports[0])&&(e.transports=(e.transports||["polling","websocket","webtransport"]).map((t=>F[t])).filter((t=>!!t))),super(t,e)}}class st extends P{doPoll(){this.et().then((t=>{if(!t.ok)return this.onError("fetch read error",t.status,t);t.text().then((t=>this.onData(t)))})).catch((t=>{this.onError("fetch read error",t)}))}doWrite(t,s){this.et(t).then((t=>{if(!t.ok)return this.onError("fetch write error",t.status,t);s()})).catch((t=>{this.onError("fetch write error",t)}))}et(t){var s;const e=void 0!==t,i=new Headers(this.opts.extraHeaders);return e&&i.set("content-type","text/plain;charset=UTF-8"),null===(s=this.socket.W)||void 0===s||s.appendCookies(i),fetch(this.uri(),{method:e?"POST":"GET",body:e?t:null,headers:i,credentials:this.opts.withCredentials?"include":"omit"}).then((t=>{var s;return null===(s=this.socket.W)||void 0===s||s.parseCookies(t.headers.getSetCookie()),t}))}}const et=tt.protocol;export{st as Fetch,X as NodeWebSocket,$ as NodeXHR,tt as Socket,Z as SocketWithUpgrade,Y as SocketWithoutUpgrade,C as Transport,_ as TransportError,X as WebSocket,V as WebTransport,$ as XHR,O as installTimerFunctions,x as nextTick,J as parse,et as protocol,F as transports};
//# sourceMappingURL=engine.io.esm.min.js.map
