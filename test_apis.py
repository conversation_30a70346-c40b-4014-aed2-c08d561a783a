"""
Test script to verify API connections and fetch sample data
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.hltv_client import HLTVClient
from api.pandascore_client import PandaScoreClient
from config import config
from utils.logger import logger
from datetime import date

def test_hltv_api():
    """Test HLTV API connection and basic functionality"""
    print("Testing HLTV API...")
    hltv = HLTVClient()
    
    # Health check
    if not hltv.health_check():
        print("❌ HLTV API server is not responding")
        return False
    
    print("✅ HLTV API server is responding")
    
    # Test upcoming matches
    try:
        upcoming = hltv.get_upcoming_matches(limit=5)
        print(f"✅ Found {len(upcoming)} upcoming matches")
        if upcoming:
            print(f"   Sample match: {upcoming[0]}")
    except Exception as e:
        print(f"❌ Error getting upcoming matches: {e}")
        return False
    
    # Test team rankings
    try:
        rankings = hltv.get_team_rankings()
        print(f"✅ Found {len(rankings)} team rankings")
        if rankings:
            print(f"   Top team: {rankings[0]}")
    except Exception as e:
        print(f"❌ Error getting team rankings: {e}")
    
    return True

def test_pandascore_api():
    """Test PandaScore API connection and basic functionality"""
    print("\nTesting PandaScore API...")
    pandascore = PandaScoreClient()
    
    # Health check
    if not pandascore.health_check():
        print("❌ PandaScore API is not responding")
        return False
    
    print("✅ PandaScore API is responding")
    
    # Test upcoming matches
    try:
        upcoming = pandascore.get_upcoming_matches(per_page=5)
        print(f"✅ Found {len(upcoming)} upcoming matches")
        if upcoming:
            print(f"   Sample match: {upcoming[0].get('name', 'Unknown')} - {upcoming[0].get('begin_at', 'No date')}")
    except Exception as e:
        print(f"❌ Error getting upcoming matches: {e}")
        return False
    
    # Test matches for target date
    try:
        target_matches = pandascore.get_matches_by_date(config.prediction.target_date, per_page=10)
        print(f"✅ Found {len(target_matches)} matches for {config.get_target_date_str()}")
        for match in target_matches[:3]:  # Show first 3 matches
            teams = f"{match.get('opponents', [{}])[0].get('opponent', {}).get('name', 'TBD')} vs {match.get('opponents', [{}])[1].get('opponent', {}).get('name', 'TBD') if len(match.get('opponents', [])) > 1 else 'TBD'}"
            print(f"   {teams} - {match.get('begin_at', 'No date')}")
    except Exception as e:
        print(f"❌ Error getting matches for target date: {e}")
    
    # Test teams
    try:
        teams = pandascore.get_teams(per_page=5)
        print(f"✅ Found {len(teams)} teams")
        if teams:
            print(f"   Sample team: {teams[0].get('name', 'Unknown')}")
    except Exception as e:
        print(f"❌ Error getting teams: {e}")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting API Tests...")
    print(f"Target date for predictions: {config.get_target_date_str()}")
    
    hltv_success = test_hltv_api()
    pandascore_success = test_pandascore_api()
    
    print("\n📊 Test Results:")
    print(f"HLTV API: {'✅ Working' if hltv_success else '❌ Failed'}")
    print(f"PandaScore API: {'✅ Working' if pandascore_success else '❌ Failed'}")
    
    if hltv_success and pandascore_success:
        print("\n🎉 All APIs are working correctly!")
        return True
    else:
        print("\n⚠️  Some APIs are not working. Please check the configuration and server status.")
        return False

if __name__ == "__main__":
    main()
