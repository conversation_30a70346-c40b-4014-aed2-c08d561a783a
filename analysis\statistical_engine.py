"""
Statistical Analysis Engine for CS2 Betting Predictions
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import math
from config import config
from utils.logger import logger

class ELOSystem:
    """ELO rating system for teams"""

    def __init__(self, k_factor: float = 32.0, initial_rating: float = 1500.0):
        self.k_factor = k_factor
        self.initial_rating = initial_rating
        self.ratings = {}

    def get_rating(self, team: str) -> float:
        """Get current ELO rating for a team"""
        return self.ratings.get(team, self.initial_rating)

    def update_ratings(self, team1: str, team2: str, team1_score: int, team2_score: int):
        """Update ELO ratings based on match result"""
        rating1 = self.get_rating(team1)
        rating2 = self.get_rating(team2)

        # Calculate expected scores
        expected1 = 1 / (1 + 10 ** ((rating2 - rating1) / 400))
        expected2 = 1 / (1 + 10 ** ((rating1 - rating2) / 400))

        # Determine actual scores (1 for win, 0.5 for draw, 0 for loss)
        if team1_score > team2_score:
            actual1, actual2 = 1.0, 0.0
        elif team2_score > team1_score:
            actual1, actual2 = 0.0, 1.0
        else:
            actual1, actual2 = 0.5, 0.5

        # Update ratings
        new_rating1 = rating1 + self.k_factor * (actual1 - expected1)
        new_rating2 = rating2 + self.k_factor * (actual2 - expected2)

        self.ratings[team1] = new_rating1
        self.ratings[team2] = new_rating2

        return new_rating1, new_rating2

    def predict_match_probability(self, team1: str, team2: str) -> Tuple[float, float]:
        """Predict win probabilities for both teams"""
        rating1 = self.get_rating(team1)
        rating2 = self.get_rating(team2)

        prob1 = 1 / (1 + 10 ** ((rating2 - rating1) / 400))
        prob2 = 1 - prob1

        return prob1, prob2

class StatisticalEngine:
    """Main statistical analysis engine"""

    def __init__(self):
        self.elo_system = ELOSystem(
            k_factor=config.prediction.elo_k_factor,
            initial_rating=config.prediction.initial_elo
        )

    def analyze_team_performance(self, team_data: Dict) -> Dict:
        """Analyze team performance metrics"""
        analysis = {
            'recent_form': self._calculate_recent_form(team_data),
            'map_performance': self._analyze_map_performance(team_data),
            'player_stats': self._analyze_player_performance(team_data),
            'consistency': self._calculate_consistency(team_data),
            'strength_of_schedule': self._calculate_strength_of_schedule(team_data)
        }

        return analysis

    def _calculate_recent_form(self, team_data: Dict) -> Dict:
        """Calculate recent form metrics"""
        recent_matches = team_data.get('recent_matches', [])
        if not recent_matches:
            # Return default values for teams with no recent match data
            return {
                'wins': 0,
                'losses': 0,
                'total_matches': 0,
                'win_rate': 0.5,  # Neutral assumption
                'form_score': 0.5  # Neutral form score
            }

        # Limit to last 15 matches
        recent_matches = recent_matches[:config.data.recent_matches_lookback]

        wins = 0
        total_matches = len(recent_matches)

        for match in recent_matches:
            # Determine if team won (this depends on the data structure)
            if self._did_team_win(match, team_data['name']):
                wins += 1

        win_rate = wins / total_matches if total_matches > 0 else 0.5

        # Calculate weighted form score (recent matches weighted more)
        form_score = 0.0
        total_weight = 0.0
        for i, match in enumerate(recent_matches):
            weight = 1.0 - (i * 0.05)  # Decrease weight by 5% for each older match
            total_weight += weight
            if self._did_team_win(match, team_data['name']):
                form_score += weight

        form_score = form_score / total_weight if total_weight > 0 else 0.5

        return {
            'wins': wins,
            'losses': total_matches - wins,
            'total_matches': total_matches,
            'win_rate': win_rate,
            'form_score': form_score
        }

    def _analyze_map_performance(self, team_data: Dict) -> Dict:
        """Analyze team performance on different maps"""
        recent_matches = team_data.get('recent_matches', [])
        map_stats = {}

        for match in recent_matches:
            # Extract map information from match data
            games = match.get('games', [])
            for game in games:
                map_name = game.get('map', {}).get('name', 'unknown')
                if map_name not in map_stats:
                    map_stats[map_name] = {'wins': 0, 'total': 0}

                map_stats[map_name]['total'] += 1
                if self._did_team_win_map(game, team_data['name']):
                    map_stats[map_name]['wins'] += 1

        # Calculate win rates
        for map_name in map_stats:
            total = map_stats[map_name]['total']
            wins = map_stats[map_name]['wins']
            map_stats[map_name]['win_rate'] = wins / total if total > 0 else 0.0

        return map_stats

    def _analyze_player_performance(self, team_data: Dict) -> Dict:
        """Analyze individual player performance"""
        players = team_data.get('players', [])
        player_analysis = {}

        for player in players:
            if player.get('active', True):
                player_analysis[player['name']] = {
                    'role': player.get('role'),
                    'age': player.get('age'),
                    'nationality': player.get('nationality'),
                    'experience': self._calculate_player_experience(player)
                }

        return player_analysis

    def _calculate_consistency(self, team_data: Dict) -> float:
        """Calculate team consistency score"""
        recent_matches = team_data.get('recent_matches', [])
        if len(recent_matches) < 5:
            return 0.5  # Default consistency for insufficient data

        # Calculate variance in performance
        scores = []
        for match in recent_matches[:10]:  # Last 10 matches
            # Simple scoring: 1 for win, 0 for loss
            score = 1.0 if self._did_team_win(match, team_data['name']) else 0.0
            scores.append(score)

        if not scores:
            return 0.5

        variance = float(np.var(scores))
        # Convert variance to consistency score (lower variance = higher consistency)
        consistency = 1.0 - min(variance * 4, 1.0)  # Scale and cap at 1.0

        return float(consistency)

    def _calculate_strength_of_schedule(self, team_data: Dict) -> float:
        """Calculate strength of schedule based on opponents faced"""
        recent_matches = team_data.get('recent_matches', [])
        if not recent_matches:
            return 0.5  # Default strength

        # This is a simplified calculation
        # In a real implementation, you'd use opponent ELO ratings
        opponent_strengths = []
        for match in recent_matches[:10]:
            # Placeholder: assign random strength based on tournament tier
            tournament_name = match.get('tournament', {}).get('name', '').lower()
            if 'major' in tournament_name or 'championship' in tournament_name:
                strength = 0.8
            elif 'qualifier' in tournament_name:
                strength = 0.4
            else:
                strength = 0.6
            opponent_strengths.append(strength)

        return float(np.mean(opponent_strengths)) if opponent_strengths else 0.5

    def _did_team_win(self, match: Dict, team_name: str) -> bool:
        """Determine if the team won the match"""
        # This depends on the data structure from the API
        # Simplified implementation
        opponents = match.get('opponents', [])
        if len(opponents) >= 2:
            for opponent in opponents:
                if opponent.get('opponent', {}).get('name') == team_name:
                    # Check if this opponent won
                    return match.get('winner', {}).get('name') == team_name
        return False

    def _did_team_win_map(self, game: Dict, team_name: str) -> bool:
        """Determine if the team won a specific map"""
        # Simplified implementation
        return game.get('winner', {}).get('name') == team_name

    def _calculate_player_experience(self, player: Dict) -> float:
        """Calculate player experience score"""
        age = player.get('age', 20)
        # Simple experience calculation based on age
        # Peak performance typically around 22-26
        if 20 <= age <= 26:
            return 1.0
        elif age < 20:
            return 0.7 + (age - 16) * 0.075  # Young players
        else:
            return max(0.5, 1.0 - (age - 26) * 0.05)  # Older players

    def calculate_head_to_head_advantage(self, team1_data: Dict, team2_data: Dict) -> Dict:
        """Calculate head-to-head advantage between two teams"""
        # This would require historical match data between the teams
        # Simplified implementation
        return {
            'team1_wins': 0,
            'team2_wins': 0,
            'total_matches': 0,
            'advantage': 0.0  # -1 to 1, where 1 means team1 has complete advantage
        }

    def predict_match_outcome(self, team1_data: Dict, team2_data: Dict) -> Dict:
        """Predict match outcome using multiple factors"""
        # Analyze both teams
        team1_analysis = self.analyze_team_performance(team1_data)
        team2_analysis = self.analyze_team_performance(team2_data)

        # Get ELO predictions
        team1_name = team1_data['name']
        team2_name = team2_data['name']
        elo_prob1, elo_prob2 = self.elo_system.predict_match_probability(team1_name, team2_name)

        # Calculate form-based probabilities
        form1 = team1_analysis['recent_form']['form_score']
        form2 = team2_analysis['recent_form']['form_score']
        form_total = form1 + form2
        form_prob1 = form1 / form_total if form_total > 0 else 0.5
        form_prob2 = form2 / form_total if form_total > 0 else 0.5

        # Combine predictions with weights
        weights = config.prediction
        final_prob1 = (
            elo_prob1 * 0.4 +  # ELO weight
            form_prob1 * weights.recent_form_weight +
            team1_analysis['consistency'] * 0.1
        )
        final_prob2 = 1.0 - final_prob1

        # Calculate confidence based on data quality
        confidence = self._calculate_prediction_confidence(team1_analysis, team2_analysis)

        return {
            'team1_win_probability': final_prob1,
            'team2_win_probability': final_prob2,
            'confidence': confidence,
            'elo_ratings': {
                'team1': self.elo_system.get_rating(team1_name),
                'team2': self.elo_system.get_rating(team2_name)
            },
            'form_scores': {
                'team1': form1,
                'team2': form2
            }
        }

    def _calculate_prediction_confidence(self, team1_analysis: Dict, team2_analysis: Dict) -> float:
        """Calculate confidence in the prediction"""
        # Base confidence on data availability and quality
        team1_matches = team1_analysis['recent_form']['total_matches']
        team2_matches = team2_analysis['recent_form']['total_matches']

        # Confidence decreases with less data
        data_confidence = min(team1_matches, team2_matches) / config.data.recent_matches_lookback

        # Confidence increases with consistency
        consistency_confidence = (
            team1_analysis['consistency'] + team2_analysis['consistency']
        ) / 2

        # Combine factors
        overall_confidence = (data_confidence * 0.6 + consistency_confidence * 0.4)

        return min(max(overall_confidence, 0.1), 1.0)  # Clamp between 0.1 and 1.0
